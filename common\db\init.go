package db

import (
	"restaurant-local-server-go/common/logger"
	"restaurant-local-server-go/common/toast"

	"github.com/glebarez/sqlite"
	"gorm.io/gorm"
)

var database *gorm.DB

func Init() {
	dbInstance, err := gorm.Open(sqlite.Open("data/test.db"), &gorm.Config{})
	if err != nil {
		toast.Notify("Database Error", "Failed to connect database")
		logger.Fatal("Failed to connect database: " + err.Error())
	}

	// Auto migrate models
	err = dbInstance.AutoMigrate($categories.Entity{})
	if err != nil {
		toast.Notify("Database Error", "Failed to migrate database")
		logger.Fatal("Failed to migrate database: " + err.Error())
	}

	database = dbInstance
	logger.Info("Database connected")
}
