package logger

import (
	"log"
)

func Info(message string) {
	log.Println("============================================")
	log.Println("[INFO] " + message)
	log.Println("--------------------------------------------")
}

func <PERSON><PERSON>r(message string) {
	log.Println("============================================")
	log.Println("[ERROR] " + message)
	log.Println("--------------------------------------------")
}

func Debug(message string) {
	log.Println("============================================")
	log.Println("[DEBUG] " + message)
	log.Println("--------------------------------------------")
}

func Warn(message string) {
	log.Println("============================================")
	log.Println("[WARN] " + message)
	log.Println("--------------------------------------------")
}

func Fatal(message string) {
	log.Println("============================================")
	log.Println("[FATAL] " + message)
	log.Fatal("--------------------------------------------")
}
