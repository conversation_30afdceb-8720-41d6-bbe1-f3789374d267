package toast

import (
	// "restaurant-local-server-go/common/constants"

	"restaurant-local-server-go/common/logger"

	"github.com/go-toast/toast"
)

func Notify(title, message string) {
	notification := toast.Notification{
		Title:   title,
		Message: message,
		AppID:   "Royaa Restaurant",
		// Icon: constants.PATH_ICON,
	}

	err := notification.Push()
	if err != nil {
		logger.Error("Failed to send notification: " + err.Error())
	}
}
